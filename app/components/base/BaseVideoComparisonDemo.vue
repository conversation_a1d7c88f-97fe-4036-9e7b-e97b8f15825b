<script setup lang="ts">
interface Props {
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px'
})

const currentExample = ref(0)
const examples = [
  {
    id: '1',
    type: 'text-to-video',
    beforeText:
      'A majestic eagle soaring through mountain peaks at sunset, golden hour lighting, cinematic shot',
    beforeImage: null,
    title: 'Text to Video Generation',
    description: 'Transform text descriptions into stunning videos with AI',
    afterContent:
      'https://fa1030eacc97e2f2ca187ef328dddf17.r2.cloudflarestorage.com/geminigen-dev-upload-bucket/20/generated_result/video/6de46cc0-6001-11f0-aee6-7a3c1801fb5d/6de46cc0-6001-11f0-aee6-7a3c1801fb5d_0.mp4?response-content-type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=9e4aaa0a83527e9fde114e51284a68ed%2F20250713%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250713T155357Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=e52f0f81d2576e0536cda18115d7c4f725dc256950b268d417a64a36b024a3d1'
  },
  {
    id: '2',
    type: 'image-to-video',
    beforeText: null,
    beforeImage:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    title: 'Image to Video Animation',
    description: 'Bring static images to life with AI video generation',
    afterContent:
      'https://fa1030eacc97e2f2ca187ef328dddf17.r2.cloudflarestorage.com/geminigen-dev-upload-bucket/20/generated_result/video/02c9ace2-6002-11f0-bcba-7a3c1801fb5d/02c9ace2-6002-11f0-bcba-7a3c1801fb5d_0.mp4?response-content-type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=9e4aaa0a83527e9fde114e51284a68ed%2F20250713%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250713T155807Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=c7252b5672f1099cc50854c345fcc26c9dec7ad712ee0a90242b65adbe951011'
  },
  {
    id: '3',
    type: 'text-to-video',
    beforeText: 'Extreme close-up of a an eye with city reflected in it.',
    beforeImage: null,
    title: 'Composition',
    description: 'Create immersive videos from text descriptions',
    afterContent:
      'https://fa1030eacc97e2f2ca187ef328dddf17.r2.cloudflarestorage.com/geminigen-dev-upload-bucket/20/generated_result/video/c1338a0e-6002-11f0-8dde-7a3c1801fb5d/c1338a0e-6002-11f0-8dde-7a3c1801fb5d_0.mp4?response-content-type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=9e4aaa0a83527e9fde114e51284a68ed%2F20250713%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250713T160324Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=0ed1569fbf2e6ff66b1b0244531b2fea8e0a14e2159afdf15b58467f7a413a34'
  }
]

const sliderPosition = ref(50)
const isDragging = ref(false)
const containerRef = ref<HTMLElement>()

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  updatePosition(event)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    updatePosition(event)
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const updatePosition = (event: MouseEvent) => {
  if (!containerRef.value) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
  sliderPosition.value = percentage
}

const nextExample = () => {
  currentExample.value = (currentExample.value + 1) % examples.length
}

const prevExample = () => {
  currentExample.value
    = currentExample.value === 0 ? examples.length - 1 : currentExample.value - 1
}

// Auto-cycle examples
let autoTimer: NodeJS.Timeout | null = null

onMounted(() => {
  autoTimer = setInterval(() => {
    nextExample()
  }, 5000)
})

onUnmounted(() => {
  if (autoTimer) {
    clearInterval(autoTimer)
  }
})

const videoRef = ref<HTMLVideoElement>()
const videoLoaded = ref(false)
// Video event handlers
const onVideoLoaded = () => {
  videoLoaded.value = true
  if (videoRef.value) {
    videoRef.value.play().catch(() => {
      // Ignore autoplay errors
    })
  }
}
</script>

<template>
  <div class="relative w-full">
    <!-- Main Comparison Area -->
    <div
      ref="containerRef"
      class="relative overflow-hidden rounded-lg cursor-ew-resize select-none border border-gray-200 dark:border-gray-700"
      :style="{ height: props.height }"
      @mousedown="handleMouseDown"
    >
      <!-- Right Side - Video Placeholder -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600"
      >
        <video
          v-if="examples[currentExample]?.afterContent"
          ref="videoRef"
          :src="examples[currentExample]?.afterContent"
          class="w-full h-full object-cover"
          muted
          loop
          autoplay
          playsinline
          @loadeddata="onVideoLoaded"
        />

        <div class="w-full h-full flex items-center justify-center">
          <div class="text-center text-white">
            <UIcon
              name="i-lucide-video"
              class="w-16 h-16 mb-4 mx-auto opacity-80"
            />
            <p class="text-lg font-medium mb-2">
              Generated Video
            </p>
            <p class="text-sm opacity-80">
              AI-powered video generation
            </p>
          </div>
        </div>
        <!-- Video Label -->
        <div
          class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium"
        >
          Generated Video
        </div>
      </div>

      <!-- Left Side - Text Prompt or Image (Clipped) -->
      <div
        class="absolute inset-0 overflow-hidden"
        :style="{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }"
      >
        <!-- Text Content -->
        <div
          v-if="examples[currentExample].type === 'text-to-video'"
          class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center p-6"
        >
          <div class="text-center max-w-md">
            <UIcon
              name="i-lucide-type"
              class="w-12 h-12 text-primary-500 mx-auto mb-4"
            />
            <p
              class="text-gray-800 dark:text-gray-200 text-lg font-medium leading-relaxed"
            >
              "{{ examples[currentExample].beforeText }}"
            </p>
          </div>
        </div>

        <!-- Image Content -->
        <div
          v-else-if="examples[currentExample].type === 'image-to-video'"
          class="w-full h-full"
        >
          <img
            :src="examples[currentExample].beforeImage"
            alt="Source Image"
            class="w-full h-full object-cover"
          >
        </div>

        <!-- Label -->
        <div
          class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium"
        >
          {{
            examples[currentExample].type === "text-to-video"
              ? "Text Prompt"
              : "Source Image"
          }}
        </div>
      </div>

      <!-- Slider Handle -->
      <div
        class="absolute top-0 bottom-0 w-1 bg-white shadow-lg z-10 cursor-ew-resize"
        :style="{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }"
      >
        <!-- Slider Circle -->
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-primary-500 flex items-center justify-center"
        >
          <div class="flex space-x-0.5">
            <div class="w-0.5 h-4 bg-primary-500 rounded-full" />
            <div class="w-0.5 h-4 bg-primary-500 rounded-full" />
          </div>
        </div>
      </div>

      <!-- Navigation Arrows -->
      <UButton
        v-if="examples.length > 1"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-30"
        @click.stop="prevExample"
        @mousedown.stop
      >
        <UIcon
          name="i-lucide-chevron-left"
          class="w-5 h-5"
        />
      </UButton>

      <UButton
        v-if="examples.length > 1"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-30"
        @click.stop="nextExample"
        @mousedown.stop
      >
        <UIcon
          name="i-lucide-chevron-right"
          class="w-5 h-5"
        />
      </UButton>

      <!-- Type Indicators -->
      <div class="absolute bottom-4 left-4">
        <div
          class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1"
        >
          <UIcon
            :name="
              examples[currentExample].type === 'text-to-video'
                ? 'i-lucide-type'
                : 'i-lucide-image'
            "
            class="w-3 h-3"
          />
          <span>{{
            examples[currentExample].type === "text-to-video" ? "Text" : "Image"
          }}</span>
        </div>
      </div>

      <div class="absolute bottom-4 right-4">
        <div
          class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1"
        >
          <UIcon
            name="i-lucide-video"
            class="w-3 h-3"
          />
          <span>Video</span>
        </div>
      </div>
    </div>

    <!-- Example Info -->
    <div class="mt-4 text-center">
      <h4 class="text-base font-medium text-gray-900 dark:text-white mb-1">
        {{ examples[currentExample].title }}
      </h4>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ examples[currentExample].description }}
      </p>
    </div>

    <!-- Dots Indicator -->
    <div
      v-if="examples.length > 1"
      class="flex justify-center mt-4 space-x-2"
    >
      <UButton
        v-for="(example, index) in examples"
        :key="example.id"
        class="w-2 h-2 rounded-full transition-all duration-200"
        :class="
          index === currentExample
            ? 'bg-primary-500'
            : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
        "
        @click="currentExample = index"
        @mousedown.stop
      />
    </div>

    <!-- Instructions -->
    <div class="mt-4 text-center">
      <p class="text-xs text-gray-500 dark:text-gray-400">
        {{
          $t(
            "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider."
          )
        }}
      </p>
    </div>
  </div>
</template>
