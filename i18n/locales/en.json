{"common.home": "Home", "common.back": "Back", "common.edit": "Edit", "common.save": "Save", "common.cancel": "Cancel", "common.delete": "Delete", "common.copy": "Copy", "common.copied": "<PERSON>pied", "common.manage": "Manage", "system": "AI Image Generator", "helloWorld": "Hello World!", "Describe the image you want to generate...": "Describe the image you want to generate...", "Describe the video you want to generate...": "Describe the video you want to generate...", "Describe the speech you want to generate...": "Describe the speech you want to generate...", "appTitle": "GeminiGen.AI", "copyright": "Copyright © {year}, GeminiGen.AI", "available": "Available for new projects", "notAvailable": "Not available at the moment", "blog": "Blog", "copyLink": "Copy link", "minRead": "MIN READ", "articleLinkCopied": "Article link copied to clipboard", "clickToClose": "Click anywhere or press ESC to close", "promptDetails": "Prompt details", "generateWithPrompt": "Generate with this prompt", "generateWithSettings": "Generate with these settings", "preset": "Preset", "style": "Style", "resolution": "Resolution", "addImage": "Add Image", "modelPreset": "Model/Preset", "imageDimensions": "Image Dimensions", "yourImage": "Your Image", "generate": "Generate", "cancel": "Cancel", "confirm": "Confirm", "listenToSpeech": "Listen to Speech", "generateSimilar": "Generate Similar", "Safety Filter": "Safety Filter", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safetyFilter.blockLowAndAbove": "Block Low and Above", "safetyFilter.blockMediumAndAbove": "Block Medium and Above", "safetyFilter.blockOnlyHigh": "Block Only High", "safetyFilter.blockNone": "Block None", "nav.aitool": "AI Tool", "nav.history": "History", "nav.orders": "Orders", "AI Image Generation Examples": "AI Image Generation Examples", "Explore the power of AI image generation with these interactive comparisons": "Explore the power of AI image generation with these interactive comparisons", "AI Video Generation Examples": "AI Video Generation Examples", "Explore the power of AI video generation with these interactive comparisons": "Explore the power of AI video generation with these interactive comparisons", "Try the Comparison!": "Try the Comparison!", "Try the Video Comparison!": "Try the Video Comparison!", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.", "Got it!": "Got it!", "Please login to access your saved prompts.": "Please login to access your saved prompts.", "Access Your Personal Voices": "Access Your Personal Voices", "Access Your Favorite Voices": "Access Your Favorite Voices", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "Login to view your favorite voices. Save voices you love and access them quickly for your projects.", "Create Account": "Create Account", "Join thousands of creators using AI voices for their projects": "Join thousands of creators using AI voices for their projects", "Duration": "Duration", "Select video duration in seconds": "Select video duration in seconds", "This setting is locked for the selected model": "This setting is locked for the selected model", "Prompts will always be refined to improve output quality (required for this model)": "Prompts will always be refined to improve output quality (required for this model)", "nav.api": "API", "nav.login": "<PERSON><PERSON>", "3D Render": "3D Render", "Acrylic": "Acrylic", "Anime General": "Anime General", "Creative": "Creative", "Dynamic": "Dynamic", "Fashion": "Fashion", "Game Concept": "Game Concept", "Graphic Design 3D": "Graphic Design 3D", "Illustration": "Illustration", "None": "None", "Portrait": "Portrait", "Portrait Cinematic": "Portrait Cinematic", "Portrait Fashion": "Portrait Fashion", "Ray Traced": "<PERSON>", "Stock Photo": "Stock Photo", "Watercolor": "Watercolor", "voice": "Voice", "emotion": "Emotion", "speed": "Speed", "speed_settings": "Speed Settings", "speed_value": "Speed Value", "speed_slider": "Speed Slider", "apply": "Apply", "speech_settings": "Speech Settings", "current_speed": "Current Speed", "reset_defaults": "Reset to Defaults", "outputFormat": "Output Format", "outputChannel": "Output Channel", "selectVoice": "Select Voice", "selectEmotion": "Select Emotion", "selectFormat": "Select Format", "selectChannel": "Select Channel", "noVoicesAvailable": "No voices available", "noEmotionsAvailable": "No emotions available", "searchVoices": "Search voices...", "searchEmotions": "Search emotions...", "noVoicesFound": "No voices found", "noEmotionsFound": "No emotions found", "retry": "Retry", "noAudioSample": "No audio sample available", "Speech Generation Complete": "Speech Generation Complete", "Your speech has been generated successfully": "Your speech has been generated successfully", "AI Image Generator": "AI Image Generator", "Generate AI images from text prompts with a magical particle transformation effect": "Generate AI images from text prompts with a magical particle transformation effect", "Enter your prompt": "Enter your prompt", "Generating...": "Generating...", "Generate Image": "Generate Image", "Enter a prompt and click Generate Image to create an AI image": "Enter a prompt and click Generate Image to create an AI image", "Prompt:": "Prompt:", "Download": "Download", "How It Works": "How It Works", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:", "Sends your prompt to an AI image generation API": "Sends your prompt to an AI image generation API", "Creates a particle system with thousands of tiny particles": "Creates a particle system with thousands of tiny particles", "Transforms the random noise particles into the generated image": "Transforms the random noise particles into the generated image", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.", "Transform": "Transform", "Transforming...": "Transforming...", "Initializing particles...": "Initializing particles...", "Loading image...": "Loading image...", "Creating particle system...": "Creating particle system...", "Adding event listeners...": "Adding event listeners...", "Ready!": "Ready!", "AI Image Particle Effect": "AI Image Particle Effect", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images", "Click anywhere or press ESC to close": "Click anywhere or press ESC to close", "auth.login": "<PERSON><PERSON>", "auth.loginDescription": "Login to your account to continue", "auth.email": "Email", "auth.enterEmail": "Enter your email", "auth.filter": "Filter", "auth.password": "Password", "auth.enterPassword": "Enter your password", "auth.rememberMe": "Remember me", "auth.welcomeBack": "Welcome back", "auth.signupFailed": "Signup failed", "auth.signupFailedDescription": "There was an error during signup. Please try again.", "auth.dontHaveAccount": "Don't have an account?", "auth.signUp": "Sign up", "auth.forgotPassword": "Forgot password?", "auth.bySigningIn": "By signing in, you agree to our", "auth.termsOfService": "Terms of Service", "auth.signUpTitle": "Sign up", "auth.signUpDescription": "Create an account to get started", "auth.name": "Name", "auth.enterName": "Enter your name", "auth.createAccount": "Create account", "auth.alreadyHaveAccount": "Already have an account?", "auth.bySigningUp": "By signing up, you agree to our", "auth.backToHome": "Back to home", "auth.notVerifyAccount": "Your account is not verified. Please verify your account to continue", "auth.verifyAccount": "Verify account", "auth.resendActivationEmail": "Resend activation email", "auth.accountRecovery": "Account Recovery", "auth.accountRecoveryTitle": "Recover your account", "auth.accountRecoveryDescription": "Enter your email to receive password reset instructions", "auth.sendRecoveryEmail": "Send recovery email", "auth.recoveryEmailSent": "Recovery email sent", "auth.recoveryEmailSentDescription": "Please check your email for password reset instructions", "auth.resetPassword": "Reset Password", "auth.resetPasswordTitle": "Reset your password", "auth.resetPasswordDescription": "Enter your new password", "auth.newPassword": "New password", "auth.confirmPassword": "Confirm password", "auth.enterNewPassword": "Enter your new password", "auth.enterConfirmPassword": "Confirm your new password", "auth.passwordResetSuccess": "Password reset successful", "auth.passwordResetSuccessDescription": "Your password has been reset successfully. You can now login with your new password", "auth.activateAccount": "Activate Account", "auth.activateAccountTitle": "Activate your account", "auth.activateAccountDescription": "Your account is being activated...", "auth.accountActivated": "Account activated", "auth.accountActivatedDescription": "Your account has been activated successfully. You can now login", "auth.activationFailed": "Activation failed", "auth.activationFailedDescription": "Failed to activate your account. Please try again or contact support", "auth.backToLogin": "Back to login", "auth.loginFailed": "<PERSON><PERSON> failed", "auth.loginWithGoogle": "Login with Google", "auth.google": "Google", "validation.invalidEmail": "Invalid email", "validation.passwordMinLength": "Password must be at least 8 characters", "validation.nameRequired": "Name is required", "validation.required": "This field is required", "validation.passwordsDoNotMatch": "Passwords do not match", "imageSelect.pleaseSelectImageFile": "Please select an image file", "imageSelect.selectedImage": "Selected image", "imageSelect.removeImage": "Remove image", "pixelReveal.loading": "Loading image...", "pixelReveal.processing": "Processing image...", "pixelReveal.revealComplete": "Image reveal complete", "SIGNIN_WRONG_EMAIL_PASSWORD": "Wrong email or password", "Try again": "Try again", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.textToImage": "Text to Image", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Generate high-quality, detailed images with accurate text rendering for creative visual content.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Express your ideas like never before — with <PERSON><PERSON>, creativity has no limits.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash is a powerful tool for generating images from text prompts.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "Greater control, consistency, and creativity than ever before.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "Video, meet audio. Our latest video generation model, designed to empower filmmakers and storytellers.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "The most advanced text-to-speech model available.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Large scale processing (e.g. multiple pdfs).\nLow latency, high volume tasks which require thinking\nAgentic use cases", "aiToolMenu.link": "Link", "aiToolMenu.linkDescription": "Use NuxtLink with superpowers.", "aiToolMenu.soon": "Soon", "readArticle": "Read Article", "switchToLightMode": "Switch to light mode", "switchToDarkMode": "Switch to dark mode", "profile": "Profile", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "buyCredits.checkout": "Checkout", "buyCredits.checkoutDescription": "Confirm your order then choose your payment method.", "buyCredits.orderDetail": "Order detail", "buyCredits.credits": "Credits", "buyCredits.pricePerUnit": "Price per unit", "buyCredits.totalCredits": "Total credits", "buyCredits.totalPrice": "Total price", "buyCredits.payment": "Payment", "buyCredits.submit": "Submit", "buyCredits.cancel": "Cancel", "pricing.title": "Pricing", "pricing.description": "Choose the perfect plan for your image generation needs", "pricing.comingSoon": "Coming Soon", "pricing.comingSoonDescription": "Our pricing plans are being finalized. Check back soon for updates.", "Budget Calculator": "Budget Calculator", "Resource Calculator": "Resource Calculator", "Budget Amount": "Budget Amount", "Resources you can generate:": "Resources you can generate:", "Select resources you want:": "Select resources you want:", "credits": "credits", "image": "image", "video": "video", "per item": "per item", "Quantity": "Quantity", "Total Cost:": "Total Cost:", "Approximately {credits} credits": "Approximately {credits} credits", "Images": "Images", "Videos": "Videos", "Pricing Calculator": "Pricing Calculator", "Calculate how many resources can you generate with your budget.": "Calculate how many resources can you generate with your budget.", "Minimum $10 required": "Minimum $10 required", "Minimum Purchase Required": "Minimum Purchase Required", "Minimum purchase amount is $10. Please increase your selection.": "Minimum purchase amount is $10. Please increase your selection.", "Minimum purchase amount is $10": "Minimum purchase amount is $10", "Please add more resources to reach the minimum purchase amount.": "Please add more resources to reach the minimum purchase amount.", "Enter exact number": "Enter exact number", "Enter budget amount": "Enter budget amount", "Min: $10": "Min: $10", "Each amount shows what you can generate with your entire budget (choose one type)": "Each amount shows what you can generate with your entire budget (choose one type)", "OR": "OR", "magicImageDemo.title": "AI Image Particle Effect", "magicImageDemo.description": "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images", "magicImageDemo.image": "Image", "magicImageDemo.aboutTitle": "About This Component", "magicImageDemo.aboutDescription": "The BaseMagicImage component uses Three.js to create a particle system that can transform between random positions and an AI-generated image. The particles move with swirling and flowing effects, creating a magical transformation.", "magicImageDemo.featuresTitle": "Features", "magicImageDemo.features.particleRendering": "Particle-based image rendering", "magicImageDemo.features.smoothTransitions": "Smooth transitions between random particle positions and image formation", "magicImageDemo.features.interactiveControls": "Interactive camera controls (drag to rotate, scroll to zoom)", "magicImageDemo.features.customizable": "Customizable particle count and animation duration", "magicImageDemo.features.automatic": "Automatic or manual transformation triggering", "magicImageDemo.howItWorksTitle": "How It Works", "magicImageDemo.howItWorksDescription": "The component analyzes the pixels of an image and creates a 3D particle system where each particle represents a pixel. Brighter pixels are positioned closer to the viewer, creating a subtle 3D effect. The particles are initially scattered randomly in 3D space, then animate to form the image when triggered.", "privacy.title": "Privacy Policy", "privacy.description": "Learn how we protect your privacy and handle your data", "privacy.informationWeCollect": "Information We Collect", "privacy.informationWeCollectDescription": "We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support.", "privacy.howWeUseInformation": "How We Use Your Information", "privacy.howWeUseInformationDescription": "We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.", "privacy.informationSharing": "Information Sharing", "privacy.informationSharingDescription": "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.", "privacy.dataSecurity": "Data Security", "privacy.dataSecurityDescription": "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.", "privacy.contactUs": "Contact Us", "privacy.contactUsDescription": "If you have any questions about this Privacy Policy, please contact us through our support channels.", "terms.title": "Terms of Service", "terms.description": "Terms and conditions for using Imagen services", "terms.acceptanceOfTerms": "1. Acceptance of Terms", "terms.acceptanceOfTermsDescription": "By accessing and using Imagen services, you accept and agree to be bound by the terms and provision of this agreement.", "terms.useOfService": "2. Use of Service", "terms.useOfServiceDescription": "You agree to use our service only for lawful purposes and in accordance with these Terms of Service.", "terms.userAccounts": "3. User Accounts", "terms.userAccountsDescription": "You are responsible for maintaining the confidentiality of your account and password.", "terms.intellectualProperty": "4. Intellectual Property", "terms.intellectualPropertyDescription": "All content and materials available on our service are protected by intellectual property rights.", "terms.termination": "5. Termination", "terms.terminationDescription": "We may terminate or suspend your account and access to the service at our sole discretion.", "terms.disclaimers": "6. <PERSON><PERSON><PERSON>", "terms.disclaimersDescription": "The service is provided 'as is' without any warranties of any kind.", "terms.contactUsTerms": "Contact Us", "terms.contactUsTermsDescription": "If you have any questions about these Terms of Service, please contact us through our support channels.", "appName": "GeminiGen.AI", "quickTopUp": "Quick top up", "customTopUp": "Custom top up", "numberOfCredits": "Number of credits", "paypal": "PayPal", "paypalDescription": "Pay securely with your PayPal account", "stripe": "Stripe", "stripeDescription": "Pay securely with <PERSON>e", "debitCreditCard": "Debit or Credit Card", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Pay with Crypto", "cryptoDescription": "Bitcoin, Ethereum, and other cryptocurrencies", "profileMenu.guide": "Guide", "profileMenu.logo": "Logo", "profileMenu.settings": "Settings", "profileMenu.integration": "Integration", "profileMenu.components": "Components", "loadingMoreItems": "Loading more items...", "history.tabs.imagen": "Imagen", "history.tabs.video": "Video", "history.tabs.speech": "Speech", "history.tabs.music": "Music", "history.tabs.history": "History", "orders.title": "Order History", "orders.description": "View your transaction and payment history", "orders.orderId": "Order ID", "orders.amount": "Amount", "orders.credits": "Credits", "orders.quantity": "Quantity", "orders.platform": "Platform", "orders.externalId": "Transaction ID", "orders.status.completed": "Completed", "orders.status.success": "Success", "orders.status.paid": "Paid", "orders.status.pending": "Pending", "orders.status.processing": "Processing", "orders.status.failed": "Failed", "orders.status.cancelled": "Cancelled", "orders.status.error": "Error", "orders.empty.title": "No orders yet", "orders.empty.description": "You haven't made any orders yet. Buy credits to start using our services.", "orders.empty.action": "Buy Credits", "orders.endOfList": "You've seen all orders", "orders.errors.fetchFailed": "Failed to load order history. Please try again.", "orders.meta.title": "Order History - Imagen AI", "orders.meta.description": "View your transaction and payment history on Imagen AI", "promptLabel": "Prompt:", "videoExamples": "Video Examples", "videoExamplesDescription": "Explore these video examples with their prompts and settings. Click on any 'Use This Prompt' button to copy the prompt to your input field.", "useThisPrompt": "Use This Prompt", "model": "Model", "duration": "Duration", "videoTypeSelection": "Select Video Type", "videoTypes.examples.tikTokDanceTrend": "TikTok Dance Trend", "videoTypes.examples.energeticDanceDescription": "Energetic dance video with vibrant colors, quick cuts, trending music, vertical format, social media style", "videoTypes.examples.instagramReel": "Instagram Reel", "videoTypes.examples.lifestyleDescription": "Lifestyle content with aesthetic visuals, smooth transitions, trendy effects, engaging storytelling", "videoTypes.examples.comedySketch": "Comedy Sketch", "videoTypes.examples.funnyComedyDescription": "Funny comedy scene with expressive characters, humorous situations, entertaining dialogue, lighthearted mood", "videoTypes.examples.productLaunchAd": "Product Launch Ad", "videoTypes.examples.professionalCorporateDescription": "Professional corporate video with executive presentation, clean office environment, business formal style", "videoTypes.examples.quickPromoVideo": "Quick Promo Video", "videoTypes.examples.fastPacedPromoDescription": "Fast-paced promotional content with efficient production, cost-effective visuals, streamlined messaging", "videoTypes.examples.birthdayGreeting": "Birthday Greeting", "videoTypes.examples.personalizedBirthdayDescription": "Personalized birthday video with festive decorations, warm lighting, celebratory atmosphere, heartfelt message", "videoTypes.examples.brandStoryVideo": "Brand Story Video", "videoTypes.examples.tutorialVideo": "Tutorial Video", "videoTypes.examples.manOnThePhone": "Man on the phone", "videoTypes.examples.runningSnowLeopard": "Running snow leopard", "videoTypes.examples.snowLeopard": "Snow leopard", "videoTypes.styles.cartoonAnimated": "Cartoon or animated style video", "videoTypes.styles.naturalDocumentary": "Natural documentary-style footage", "videoTypes.styles.naturalLifelike": "Natural, lifelike video style", "videoTypes.styles.professionalMovieQuality": "Professional movie-like quality with dramatic lighting", "videoTypes.styles.creativeStylized": "Creative and stylized video effects", "videoTypes.styles.retroVintage": "Retro or vintage video aesthetic", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.time.justNow": "Just now", "notifications.time.minutesAgo": "{minutes}m ago", "notifications.time.hoursAgo": "{hours}h ago", "notifications.time.yesterday": "Yesterday", "notifications.status.processing.title": "Processing", "notifications.status.processing.description": "Your request is being processed", "notifications.status.success.title": "Completed", "notifications.status.success.description": "Successfully completed", "notifications.status.failed.title": "Failed", "notifications.status.failed.description": "An error occurred during processing", "notifications.status.warning.title": "Warning", "notifications.status.warning.description": "Completed with warnings", "notifications.status.pending.title": "Pending", "notifications.status.pending.description": "Waiting to be processed", "notifications.status.cancelled.title": "Cancelled", "notifications.status.cancelled.description": "Request was cancelled", "notifications.types.video_1.title": "Video Generation Pending", "notifications.types.video_1.description": "Video generation is waiting to be processed", "notifications.types.video_2.title": "Video Generation Complete", "notifications.types.video_2.description": "Video has been generated successfully", "notifications.types.video_3.title": "Video Generation Failed", "notifications.types.video_3.description": "Video generation failed", "notifications.types.image_1.title": "Image Generation Pending", "notifications.types.image_1.description": "Image generation is waiting to be processed", "notifications.types.image_2.title": "Image Generation Complete", "notifications.types.image_2.description": "Image has been generated successfully", "notifications.types.image_3.title": "Image Generation Failed", "notifications.types.image_3.description": "Image generation failed", "notifications.types.tts_history_1.title": "Audio Generation Pending", "notifications.types.tts_history_1.description": "Text-to-speech is waiting to be processed", "notifications.types.tts_history_2.title": "Audio Generation Complete", "notifications.types.tts_history_2.description": "Text-to-speech audio has been generated successfully", "notifications.types.tts_history_3.title": "Audio Generation Failed", "notifications.types.tts_history_3.description": "Text-to-speech generation failed", "notifications.types.voice_training_1.title": "Voice Training Pending", "notifications.types.voice_training_1.description": "Voice training is waiting to be processed", "notifications.types.voice_training_2.title": "Voice Training Complete", "notifications.types.voice_training_2.description": "Custom voice model training has finished successfully", "notifications.types.voice_training_3.title": "Voice Training Failed", "notifications.types.voice_training_3.description": "Voice training failed", "notifications.types.music_1.title": "Music Generation Pending", "notifications.types.music_1.description": "Music generation is waiting to be processed", "notifications.types.music_2.title": "Music Generation Complete", "notifications.types.music_2.description": "AI music has been generated successfully", "notifications.types.music_3.title": "Music Generation Failed", "notifications.types.music_3.description": "Music generation failed", "notifications.types.speech_1.title": "Speech Generation Pending", "notifications.types.speech_1.description": "Your speech generation request is waiting to be processed", "notifications.types.speech_2.title": "Speech Generation Complete", "notifications.types.speech_2.description": "Your speech has been generated successfully", "notifications.types.speech_3.title": "Speech Generation Failed", "notifications.types.speech_3.description": "Your speech generation failed. Please try again", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "historyFilter.all": "All", "historyFilter.imagen": "Imagen", "historyFilter.videoGen": "Video Gen", "historyFilter.speechGen": "Speech Gen", "historyFilter.dialogueGen": "Dialogue Gen", "historyFilter.speechGenDocument": "Speech Gen from Document", "historyPages.imagenDescription": "Browse your AI-generated images and artwork", "historyPages.musicDescription": "Browse your AI-generated music and audio content", "historyPages.speechDescription": "Browse your AI-generated speech and voice content", "historyPages.videoDescription": "Browse your AI-generated videos and animations", "historyPages.imagenBreadcrumb": "Imagen", "historyPages.musicBreadcrumb": "Music", "historyPages.speechBreadcrumb": "Speech", "historyPages.videoBreadcrumb": "Video Generation", "historyPages.endOfImagesHistory": "You've reached the end of the images history", "historyPages.endOfHistory": "You've reached the end of the history", "historyPages.endOfMusicHistory": "You've reached the end of the music history", "historyPages.endOfSpeechHistory": "You've reached the end of the speech history", "historyPages.endOfVideoHistory": "You've reached the end of the video history", "historyPages.noVideosFound": "No videos found", "historyPages.noVideosFoundDescription": "Start generating videos to see them here.", "historyPages.backToLibrary": "Back to Library", "historyPages.errorLoadingVideo": "Error Loading Video", "historyPages.loadingVideoDetails": "Loading video details...", "historyPages.videoDetails": "Video Details", "historyPages.videoInformation": "Video Information", "historyPages.videoNotFound": "The video you are looking for could not be found or loaded.", "historyPages.aiContentLibraryTitle": "AI Content Library", "historyPages.aiContentLibraryDescription": "Browse and manage your AI-generated content across different categories", "demo.notifications.title": "Notification Types & Status Demo", "demo.notifications.description": "Examples of different notification types with various status states", "demo.notifications.statusLegend": "Status Legend", "demo.notifications.availableNotificationTypes": "Available Notification Types", "demo.speechVoiceSelect.title": "Speech Voice Select Demo", "demo.speechVoiceSelect.description": "Demonstrating the reusable BaseSpeechVoiceSelectModal component with modelValue props", "demo.speechVoiceSelect.example1": "Example 1: <PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.example2": "Example 2: Small Size", "demo.speechVoiceSelect.example3": "Example 3: Large Size", "demo.speechVoiceSelect.example4": "Example 4: Multiple Error Examples", "demo.speechVoiceSelect.example5": "Example 5: Status Comparison", "demo.speechVoiceSelect.mainNarrator": "Main Narrator", "demo.speechVoiceSelect.characterVoice": "Character Voice", "demo.speechVoiceSelect.selectedVoicesSummary": "Selected Voices Summary:", "demo.speechVoiceSelect.clearAll": "Clear All", "demo.speechVoiceSelect.setRandomVoices": "Set Random Voices", "demo.speechVoiceSelect.logToConsole": "Log to Console", "demo.speechVoiceSelect.notSelected": "Not selected", "demo.speechVoiceSelect.voiceSelectionsChanged": "Voice selections changed", "demo.historyWrapper.title": "HistoryWrapper Status Badge Demo", "demo.historyWrapper.normalStatus": "Example 1: Normal Status (status = 1)", "demo.historyWrapper.processingStatus": "Example 2: Processing Status (status = 2)", "demo.historyWrapper.errorStatus": "Example 3: Error Status (status = 3) - Shows Error Badge", "demo.historyWrapper.multipleErrorExamples": "Example 4: Multiple Error Examples", "demo.historyWrapper.statusComparison": "Example 5: Status Comparison", "demo.historyWrapper.normalImageGeneration": "Normal Image Generation", "demo.historyWrapper.videoGenerationInProgress": "Video Generation in Progress", "demo.historyWrapper.speechGenerationFailed": "Speech Generation Failed", "demo.historyWrapper.imageFailed": "Image Failed", "demo.historyWrapper.videoFailed": "Video Failed", "demo.historyWrapper.speechFailed": "Speech Failed", "demo.historyWrapper.statusSuccess": "Status: Success", "demo.historyWrapper.statusProcessing": "Status: Processing", "demo.historyWrapper.statusError": "Status: Error", "demo.historyWrapper.status1Success": "Status 1: Success", "demo.historyWrapper.status2Processing": "Status 2: Processing", "demo.historyWrapper.badgeBehavior": "Badge Behavior:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Shows only type and style badges", "demo.historyWrapper.showsTypeStyleAndError": "Shows type, style, and red error badge with alert icon", "demo.historyWrapper.redBackgroundWithWhite": "Red background with white text and alert circle icon", "demo.historyWrapper.allBadgesHideOnHover": "All badges hide on hover to show overlay content", "demo.speechVoiceCaching.title": "Speech Voice Caching Test", "demo.speechVoiceCaching.description": "Test để kiểm tra việc cache voices giữa các components khác nhau", "demo.speechVoiceCaching.component1Modal": "Component 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Component 3 - Regular Select", "demo.speechVoiceCaching.forceReloadVoices": "Force Reload Voices", "demo.speechVoiceCaching.clearAllSelections": "Clear All Selections", "demo.speechVoiceCaching.logStoreState": "Log Store State", "demo.speechVoiceCaching.refreshPageInstructions": "Refresh page và mở bất kỳ component nào - sẽ load lại", "demo.speechVoiceCaching.checkNetworkTab": "Kiểm tra Network tab để xác nhận API calls", "demo.speechVoiceCaching.selectedVoicePersist": "Selected voice sẽ đ<PERSON><PERSON><PERSON> persist qua localStorage", "demo.speechVoiceCaching.pageMounted": "Page mounted, store will auto-initialize if needed", "settings": "Settings", "userMenu.profile": "Profile", "userMenu.buyCredits": "Buy Credits", "userMenu.settings": "Settings", "userMenu.api": "API", "userMenu.logout": "Logout", "userMenu.greeting": "Hi, {name}", "integration.title": "Integration", "integration.subtitle": "Manage your API keys and integration settings", "integration.apiKeys": "API Keys", "integration.apiKeysDescription": "Manage your API keys for programmatic access", "integration.webhook": "Webhook", "integration.webhookDescription": "Configure webhook URL for notifications", "apiKeys.title": "API Keys", "apiKeys.subtitle": "Manage your API keys for programmatic access", "apiKeys.create": "Create API Key", "apiKeys.createNew": "Create New API Key", "apiKeys.createFirst": "Create First API Key", "apiKeys.name": "Name", "apiKeys.nameDescription": "Give your API key a descriptive name", "apiKeys.namePlaceholder": "e.g., My App API Key", "apiKeys.nameRequired": "API key name is required", "apiKeys.createdAt": "Created", "apiKeys.noKeys": "No API Keys", "apiKeys.noKeysDescription": "Create your first API key to get started with programmatic access", "apiKeys.created": "API key created successfully", "apiKeys.createError": "Failed to create API key", "apiKeys.deleted": "API key deleted successfully", "apiKeys.deleteError": "Failed to delete API key", "apiKeys.deleteConfirm": "Delete API Key", "apiKeys.deleteWarning": "Are you sure you want to delete this API key? This action cannot be undone.", "apiKeys.copied": "API key copied to clipboard", "apiKeys.copyError": "Failed to copy API key", "apiKeys.successTitle": "API Key Created Successfully", "apiKeys.importantNotice": "Important Notice", "apiKeys.copyWarning": "This is the only time you will be able to view and copy this API key. Please copy it now and store it securely.", "apiKeys.key": "API Key", "apiKeys.securityTip": "Security Tips:", "apiKeys.tip1": "Store this key in a secure location", "apiKeys.tip2": "Never share your API key publicly", "apiKeys.tip3": "If compromised, delete this key and create a new one", "apiKeys.copyFirst": "Copy API Key First", "common.done": "Done", "webhook.title": "Webhook Configuration", "webhook.subtitle": "Configure webhook URL for real-time notifications", "webhook.configuration": "Webhook URL", "webhook.currentUrl": "Current Webhook URL", "webhook.currentUrlDescription": "This URL will receive POST requests for webhook events", "webhook.notConfigured": "No webhook URL configured", "webhook.url": "Webhook URL", "webhook.urlDescription": "Enter the URL where you want to receive webhook notifications", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "Please enter a webhook URL first", "webhook.invalidUrl": "Please enter a valid URL", "webhook.saved": "Webhook URL saved successfully", "webhook.saveError": "Failed to save webhook URL", "webhook.test": "Test", "webhook.testSent": "Test Sent", "webhook.testDescription": "Test webhook sent successfully", "webhook.information": "Webhook Information", "webhook.howItWorks": "How it works", "webhook.description": "When configured, we will send HTTP POST requests to your webhook URL whenever certain events occur in your account.", "webhook.events": "Webhook Events", "webhook.imageGenerated": "Image generation completed", "webhook.imageGenerationFailed": "Image generation failed", "webhook.creditUpdated": "Credit balance updated", "webhook.payloadFormat": "Payload Format", "webhook.payloadDescription": "Webhook requests will be sent as JSON with the following structure:", "webhook.security": "Security", "webhook.securityDescription": "We recommend using HTTPS URLs and implementing signature verification to ensure webhook authenticity.", "error.general": "Error", "error.validation": "Validation Error", "error.required": "Required field", "success.saved": "Saved successfully", "success.created": "Created successfully", "success.deleted": "Deleted successfully", "success.copied": "Copied to clipboard", "confirmDelete": "Confirm Delete", "confirmDeleteDescription": "Are you sure you want to delete this item? This action cannot be undone.", "historyDeleted": "History item deleted successfully", "deleteError": "Failed to delete history item", "Regenerate Image": "Regenerate Image", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?", "Yes, Regenerate": "Yes, <PERSON><PERSON><PERSON>", "Cancel": "Cancel", "models.imagen4Fast": "Imagen 4 Fast", "models.imagen4Ultra": "Imagen 4 Ultra", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Stereo", "options.allow": "Allow", "options.dontAllow": "Don't Allow", "options.voices": "Voices", "options.pickVoice": "Pick a voice", "voiceTypes.favoriteVoices": "Favorite Voices", "voiceTypes.geminiVoices": "Gemini Voices", "voiceTypes.systemVoices": "System Voices", "voiceTypes.customVoices": "Custom Voices", "voiceTypes.premiumVoices": "Premium Voices", "voiceTypes.userVoices": "User Voices", "aspectRatio": "Aspect Ratio", "Image Reference": "Image Reference", "Person Generation": "Person Generation", "safety_filter_level": "Safety Filter Level", "used_credit": "Used Credit", "downloadImage": "Download Image", "noImageAvailable": "No image available", "enhancePrompt": "Enhance Prompt", "addImages": "Add Images", "generateVideo": "Generate Video", "happy": "Happy", "sad": "Sad", "angry": "Angry", "excited": "Excited", "laughing": "Laughing", "crying": "Crying", "calm": "Calm", "serious": "Serious", "frustrated": "Frustrated", "hopeful": "Hopeful", "narrative": "Narrative", "kids' storytelling": "Kids' Storytelling", "audiobook": "Audiobook", "poetic": "Poetic", "mysterious": "Mysterious", "inspirational": "Inspirational", "surprised": "Surprised", "confident": "Confident", "romantic": "Romantic", "scared": "Scared", "trailer voice": "Trailer Voice", "advertising": "Advertising", "speech.dialogueGeneration.complete": "Dialogue Generation Complete", "speech.dialogueGeneration.failed": "Dialogue Generation Failed", "speech.dialogueGeneration.pending": "Dialogue Generation Pending", "speech.dialogueGeneration.dialogueGen": "Dialogue Gen", "speech.dialogueGeneration.successMessage": "Your dialogue has been generated successfully", "speech.speechGeneration.complete": "Speech Generation Complete", "speech.speechGeneration.failed": "Speech Generation Failed", "speech.speechGeneration.pending": "Speech Generation Pending", "speech.speechGeneration.successMessage": "Your speech has been generated successfully", "speech.speechGeneration.requestWaiting": "Your speech generation request is waiting to be processed", "speech.errors.failedToLoadEmotions": "Failed to load emotions", "documentary": "Documentary", "newsreader": "Newsreader", "weather report": "Weather Report", "game commentary": "Game Commentary", "interactive": "Interactive", "customer support": "Customer Support", "playful": "Playful", "tired": "Tired", "sarcastic": "Sarcastic", "disgusted": "Disgusted", "whispering": "Whispering", "persuasive": "Persuasive", "nostalgic": "Nostalgic", "meditative": "Meditative", "announcement": "Announcement", "professional pitch": "Professional Pitch", "casual": "Casual", "exciting trailer": "Exciting Trailer", "dramatic": "Dramatic", "corporate": "Corporate", "tech enthusiast": "Tech Enthusiast", "youthful": "Youthful", "calming reassurance": "Calming Reassurance", "heroic": "Heroic", "festive": "Festive", "urgent": "<PERSON><PERSON>", "motivational": "Motivational", "friendly": "Friendly", "energetic": "Energetic", "serene": "<PERSON><PERSON>", "bold": "Bold", "charming": "<PERSON><PERSON>", "monotone": "Monotone", "questioning": "Questioning", "directive": "Directive", "dreamy": "<PERSON>y", "epic": "Epic", "lyrical": "Lyrical", "mystical": "Mystical", "melancholy": "<PERSON><PERSON><PERSON><PERSON>", "cheerful": "Cheerful", "eerie": "Eerie", "flirtatious": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thoughtful": "Thoughtful", "cinematic": "Cinematic", "humorous": "Humorous", "instructional": "Instructional", "conversational": "Conversational", "apologetic": "Apologetic", "excuse-making": "Excuse-making", "encouraging": "Encouraging", "neutral": "Neutral", "authoritative": "Authoritative", "sarcastic cheerful": "Sarcastic Cheerful", "reassuring": "Reassuring", "formal": "Formal", "anguished": "Anguished", "giggling": "Giggling", "exaggerated": "Exaggerated", "cold": "Cold", "hot-tempered": "Hot-tempered", "grateful": "Grateful", "regretful": "Regretful", "provocative": "Provocative", "triumphant": "Triumphant", "vengeful": "Vengeful", "heroic narration": "Heroic Narration", "villainous": "Villainous", "hypnotic": "Hypnotic", "desperate": "Desperate", "lamenting": "Lamenting", "celebratory": "Celebratory", "teasing": "Teasing", "exhausted": "Exhausted", "questioning suspicious": "Questioning Suspicious", "optimistic": "Optimistic", "bright, gentle voice, expressing excitement.": "Bright, gentle voice, expressing excitement.", "low, slow voice, conveying deep emotions.": "Low, slow voice, conveying deep emotions.", "sharp, exaggerated voice, expressing frustration.": "Sharp, exaggerated voice, expressing frustration.", "fast, lively voice, full of enthusiasm.": "Fast, lively voice, full of enthusiasm.", "interrupted, joyful voice, interspersed with laughter.": "Interrupted, joyful voice, interspersed with laughter.", "shaky, low voice, expressing pain.": "Shaky, low voice, expressing pain.", "gentle, steady voice, providing reassurance.": "Gentle, steady voice, providing reassurance.", "mature, clear voice, suitable for formal content.": "Mature, clear voice, suitable for formal content.", "weary, slightly irritated voice.": "Weary, slightly irritated voice.", "bright voice, conveying positivity and hope.": "Bright voice, conveying positivity and hope.", "natural, gentle voice with a slow rhythm.": "Natural, gentle voice with a slow rhythm.", "lively, engaging voice, captivating for children.": "Lively, engaging voice, captivating for children.", "even, slow voice, emphasizing content meaning.": "Even, slow voice, emphasizing content meaning.", "rhythmic, emotional voice, conveying subtlety.": "Rhythmic, emotional voice, conveying subtlety.", "low, slow voice, evoking curiosity.": "Low, slow voice, evoking curiosity.", "strong, passionate voice, driving action.": "Strong, passionate voice, driving action.", "high, interrupted voice, expressing astonishment.": "High, interrupted voice, expressing astonishment.", "firm, powerful voice, persuasive and assuring.": "Firm, powerful voice, persuasive and assuring.", "sweet, gentle voice, suitable for emotional content.": "Sweet, gentle voice, suitable for emotional content.", "shaky, interrupted voice, conveying anxiety.": "Shaky, interrupted voice, conveying anxiety.", "deep, strong voice with emphasis, creating suspense.": "Deep, strong voice with emphasis, creating suspense.", "engaging, lively voice, emphasizing product benefits.": "Engaging, lively voice, emphasizing product benefits.", "formal, clear voice with focus on key points.": "Formal, clear voice with focus on key points.", "calm, profound voice, delivering authenticity.": "Calm, profound voice, delivering authenticity.", "standard, neutral voice, clear and precise.": "Standard, neutral voice, clear and precise.", "bright, neutral voice, suitable for concise updates.": "Bright, neutral voice, suitable for concise updates.", "fast, lively voice, stimulating excitement.": "Fast, lively voice, stimulating excitement.", "friendly, approachable voice, encouraging engagement.": "Friendly, approachable voice, encouraging engagement.", "empathetic, gentle voice, easy to connect with.": "Empathetic, gentle voice, easy to connect with.", "clear voice, emphasizing questions and answers.": "Clear voice, emphasizing questions and answers.", "cheerful, playful voice with a hint of mischief.": "Cheerful, playful voice with a hint of mischief.", "slow, soft voice lacking energy.": "Slow, soft voice lacking energy.", "ironic, sharp voice, sometimes humorous.": "Ironic, sharp voice, sometimes humorous.", "cold voice, clearly expressing discomfort.": "Cold voice, clearly expressing discomfort.", "soft, mysterious voice, creating intimacy.": "Soft, mysterious voice, creating intimacy.", "emotional voice, convincing the listener to act.": "Emotional voice, convincing the listener to act.", "gentle voice, evoking feelings of reminiscence.": "Gentle voice, evoking feelings of reminiscence.", "even, relaxing voice, suitable for mindfulness.": "Even, relaxing voice, suitable for mindfulness.", "clear voice, emphasizing key words.": "Clear voice, emphasizing key words.", "confident, clear voice, ideal for business presentations.": "Confident, clear voice, ideal for business presentations.", "natural, friendly voice, as if talking to a friend.": "Natural, friendly voice, as if talking to a friend.", "fast, powerful voice, creating tension and excitement.": "Fast, powerful voice, creating tension and excitement.", "emphasized, suspenseful voice, creating intensity.": "Emphasized, suspenseful voice, creating intensity.", "professional, formal voice, suitable for business content.": "Professional, formal voice, suitable for business content.", "energetic, lively voice, introducing new technologies.": "Energetic, lively voice, introducing new technologies.", "vibrant, cheerful voice, appealing to younger audiences.": "Vibrant, cheerful voice, appealing to younger audiences.", "gentle, empathetic voice, easing concerns.": "Gentle, empathetic voice, easing concerns.", "strong, decisive voice, full of inspiration.": "Strong, decisive voice, full of inspiration.", "bright, excited voice, suitable for celebrations.": "Bright, excited voice, suitable for celebrations.", "fast, strong voice, emphasizing urgency.": "Fast, strong voice, emphasizing urgency.", "passionate, inspiring voice, encouraging action.": "Passionate, inspiring voice, encouraging action.", "warm, approachable voice, fostering connection.": "Warm, approachable voice, fostering connection.", "fast, powerful voice, brimming with enthusiasm.": "Fast, powerful voice, brimming with enthusiasm.", "slow, gentle voice, evoking peace and tranquility.": "Slow, gentle voice, evoking peace and tranquility.", "firm, assertive voice, exuding confidence.": "Firm, assertive voice, exuding confidence.", "warm, captivating voice, leaving a strong impression.": "Warm, captivating voice, leaving a strong impression.", "flat, unvaried voice, conveying neutrality or irony.": "Flat, unvaried voice, conveying neutrality or irony.", "curious voice, emphasizing questions.": "Curious voice, emphasizing questions.", "firm, clear voice, guiding the listener step-by-step.": "Firm, clear voice, guiding the listener step-by-step.", "gentle, slow voice, evoking a floating sensation.": "Gentle, slow voice, evoking a floating sensation.", "deep, resonant voice, emphasizing grandeur.": "Deep, resonant voice, emphasizing grandeur.", "soft, melodic voice, similar to singing.": "Soft, melodic voice, similar to singing.", "low, drawn-out voice, evoking mystery.": "Low, drawn-out voice, evoking mystery.", "slow, low voice, conveying deep sadness.": "Slow, low voice, conveying deep sadness.", "bright, energetic voice, full of positivity.": "Bright, energetic voice, full of positivity.", "low, whispery voice, evoking fear or strangeness.": "Low, whispery voice, evoking fear or strangeness.", "sweet, teasing voice, full of allure.": "Sweet, teasing voice, full of allure.", "slow, reflective voice, full of contemplation.": "Slow, reflective voice, full of contemplation.", "resonant, emphasized voice, creating a movie-like effect.": "Resonant, emphasized voice, creating a movie-like effect.", "lighthearted, cheerful voice, sometimes exaggerated.": "Lighthearted, cheerful voice, sometimes exaggerated.", "clear, slow voice, guiding the listener step-by-step.": "Clear, slow voice, guiding the listener step-by-step.", "natural voice, as if chatting with the listener.": "Natural voice, as if chatting with the listener.", "soft, sincere voice, expressing regret.": "Soft, sincere voice, expressing regret.", "hesitant, uncertain voice, sometimes awkward.": "Hesitant, uncertain voice, sometimes awkward.", "warm voice, providing motivation and support.": "Warm voice, providing motivation and support.", "even voice, free of emotional bias.": "Even voice, free of emotional bias.", "strong, powerful voice, exuding credibility.": "Strong, powerful voice, exuding credibility.", "cheerful voice with an undertone of mockery.": "Cheerful voice with an undertone of mockery.", "gentle, empathetic voice, providing comfort.": "Gentle, empathetic voice, providing comfort.", "clear, polite voice, suited for formal occasions.": "Clear, polite voice, suited for formal occasions.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON>, shaky voice, expressing distress.", "interrupted voice, mixed with light laughter.": "Interrupted voice, mixed with light laughter.", "loud, emphasized voice, often humorous.": "Loud, emphasized voice, often humorous.", "flat, unemotional voice, conveying detachment.": "Flat, unemotional voice, conveying detachment.", "fast, sharp voice, sometimes out of control.": "Fast, sharp voice, sometimes out of control.", "warm, sincere voice, expressing appreciation.": "Warm, sincere voice, expressing appreciation.", "low, subdued voice, full of remorse.": "Low, subdued voice, full of remorse.", "challenging, strong voice, full of insinuation.": "Challenging, strong voice, full of insinuation.", "loud, powerful voice, full of victory.": "Loud, powerful voice, full of victory.", "low, cold voice, expressing determination for revenge.": "Low, cold voice, expressing determination for revenge.", "strong, inspiring voice, emphasizing heroic deeds.": "Strong, inspiring voice, emphasizing heroic deeds.", "low, drawn-out voice, full of scheming.": "Low, drawn-out voice, full of scheming.", "even, repetitive voice, drawing the listener in.": "Even, repetitive voice, drawing the listener in.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON>, shaky voice, expressing hopelessness.", "low, sorrowful voice, as if mourning.": "Low, sorrowful voice, as if mourning.", "excited, joyful voice, full of festive spirit.": "Excited, joyful voice, full of festive spirit.", "light, playful voice, sometimes mockingly.": "Light, playful voice, sometimes mockingly.", "weak, broken voice, expressing extreme fatigue.": "Weak, broken voice, expressing extreme fatigue.", "slow, emphasized voice, full of suspicion.": "Slow, emphasized voice, full of suspicion.", "bright, hopeful voice, creating positivity.": "Bright, hopeful voice, creating positivity.", "Your audio will be processed with the latest stable model.": "Your audio will be processed with the latest stable model.", "Your audio will be processed with the latest beta model.": "Your audio will be processed with the latest beta model.", "You don't have any saved prompts yet.": "You don't have any saved prompts yet.", "Commercial Use": "Commercial Use", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.", "This field is required.": "This field is required.", "Must be at least 8 characters": "Must be at least 8 characters", "Passwords must be different": "Passwords must be different", "Passwords must match": "Passwords must match", "Current password": "Current password", "New password": "New password", "Confirm new password": "Confirm new password", "Update": "Update", "Password": "Password", "Confirm your current password before setting a new one.": "Confirm your current password before setting a new one.", "Account": "Account", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.", "Delete account": "Delete account", "Your saved prompts": "Your saved prompts", "Custom prompt {count}": "Custom prompt {count}", "Success": "Success", "Saved prompt successfully": "Saved prompt successfully", "Error": "Error", "Saved prompt failed": "Saved prompt failed", "Updated prompt successfully": "Updated prompt successfully", "Updated prompt failed": "Updated prompt failed", "Prompt": "Prompt", "Are you sure you want to delete this prompt?": "Are you sure you want to delete this prompt?", "Deleted prompt successfully": "Deleted prompt successfully", "Deleted prompt failed": "Deleted prompt failed", "Custom prompt": "Custom prompt", "Enter your custom prompt here.": "Enter your custom prompt here.", "Prompt name": "Prompt name", "This name will help you identify your prompt.": "This name will help you identify your prompt.", "Ex: Funny prompt": "Ex: Funny prompt", "Discard": "Discard", "Ok, save it!": "Ok, save it!", "Save as new": "Save as new", "Don't use": "Don't use", "Use": "Use", "Edit": "Edit", "Other people’s privacy": "Other people’s privacy", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.", "{price}$ per credit": "{price}$ per credit", "Pricing": "Pricing", "Simple and flexible. Only pay for what you use.": "Simple and flexible. Only pay for what you use.", "Pay as you go": "Pay as you go", "Flexible": "Flexible", "Input characters": "Input characters", "Audio model": "Audio model", "Credits": "Credits", "Cost": "Cost", "HD quality voices": "HD quality voices", "Advanced model": "Advanced model", "Buy now": "Buy now", "Paste your text to calculate": "Paste your text to calculate", "Paste your text here...": "Paste your text here...", "Video Gen": "Video Gen", "Generate videos from text prompts and images.": "Generate videos from text prompts and images.", "Speech Gen": "Speech Gen", "Convert text and documents to natural speech.": "Convert text and documents to natural speech.", "Dialogue Gen": "Dialogue Gen", "Create natural conversations with multiple speakers.": "Create natural conversations with multiple speakers.", "Veo 2": "Veo 2", "Text to Video": "Text to Video", "Image to Video": "Image to Video", "Up to 8 seconds": "Up to 8 seconds", "1080p Quality": "1080p Quality", "Multiple Styles": "Multiple Styles", "Text to Speech": "Text to Speech", "Document to Speech": "Document to Speech", "Multi-Speaker Support": "Multi-Speaker Support", "50+ Voices": "50+ Voices", "Multiple Languages": "Multiple Languages", "Emotion Control": "Emotion Control", "Multi-Speaker Dialogue": "Multi-Speaker Dialogue", "Natural Conversations": "Natural Conversations", "Voice Customization": "Voice Customization", "Emotion Expression": "Emotion Expression", "Script Generation": "Script Generation", "Audio Export": "Audio Export", "Calculate": "Calculate", "Estimate your cost by drag the slider below or": "Estimate your cost by drag the slider below or", "calming": "Calming", "customer": "Customer", "exciting": "exciting", "excuse": "Excuse", "game": "Game", "hot": "Hot", "kids": "Kids", "professional": "Professional", "tech": "Tech", "trailer": "Trailer", "weather": "Weather", "No thumbnail available": "No thumbnail available", "Debit or Credit Card": "Debit or Credit Card", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express and more", "Top up now": "Top up now", "noVideoAvailable": "No video available", "assignVoicesToSpeakers": "Assign Voices to Speakers", "speakers": "Speakers", "addSpeaker": "Add Speaker", "noVoiceAssigned": "No voice assigned", "noSpeakersAdded": "No speakers added yet", "assignVoiceToSpeaker": "Assign voice to {speaker}", "assigned": "Assigned", "assign": "Assign", "editSpeaker": "Edit Speaker", "speakerName": "Speaker Name", "enterSpeakerName": "Enter speaker name", "save": "Save", "speaker": "Speaker", "assignVoices": "Assign Voices", "speakersWithVoices": "{assigned}/{total} speakers have voices", "dialogs": "Dialogs", "addDialog": "Add Dialog", "enterDialogText": "Enter dialog text...", "selectSpeaker": "Select Speaker", "generateDialogSpeech": "Generate Dialog Speech", "voice 1": "Voice 1", "voice 2": "Voice 2", "uuid": "UUID", "output_format": "Output Format", "output_channel": "Output Channel", "file_name": "File Name", "file_size": "File Size", "speakers_count": "Speakers Count", "custom_prompt": "Custom Prompt", "Please wait a moment...": "Please wait a moment...", "Click to copy": "Click to copy", "Copied to clipboard": "Copied to clipboard", "UUID has been copied to clipboard": "UUID has been copied to clipboard", "Credits: {credits} remaining": "Credits: {credits} remaining", "This generation will cost: {cost} Credits": "This generation will cost: {cost} Credits", "Your generated video will appear here": "Your generated video will appear here", "Regenerate Video": "Regenerate Video", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?", "Your generated speech will appear here": "Your generated speech will appear here", "Regenerate Speech": "Regenerate Speech", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?", "Generated Speech": "Generated Speech", "Generating speech...": "Generating speech...", "View Details": "View Details", "Speech Examples": "Speech Examples", "Click on any example to use its prompt for speech generation": "Click on any example to use its prompt for speech generation", "Click to use": "Click to use", "Style Description": "Style Description", "Dialog Content": "Dialog Content", "Your generated dialog will appear here": "Your generated dialog will appear here", "Regenerate Dialog": "Regenerate Dialog", "Generated Dialog": "Generated Dialog", "Generating dialog...": "Generating dialog...", "Dialog Information": "Dialog Information", "Audio Player": "Audio Player", "Voices": "Voices", "Voice 1": "Voice 1", "Voice 2": "Voice 2", "Dialog Examples": "Dialog Examples", "Click on any example to use its style or dialog content": "Click on any example to use its style or dialog content", "Use Style": "Use Style", "Use Dialog": "Use Dialog", "videoStyles.selectVideoStyle": "Select Video Style", "videoStyles.cinematic": "Cinematic", "videoStyles.realistic": "Realistic", "videoStyles.animated": "Animated", "videoStyles.artistic": "Artistic", "videoStyles.documentary": "Documentary", "videoStyles.vintage": "Vintage", "ui.buttons.downloadApp": "Download App", "ui.buttons.signUp": "Sign Up", "ui.buttons.viewDetails": "View Details", "ui.buttons.seeLater": "See Later", "ui.buttons.selectFile": "Select File", "ui.buttons.selectFiles": "Select Files", "ui.buttons.pickAVoice": "Pick a voice", "ui.buttons.topUpNow": "Top up now", "ui.buttons.pressEscToClose": "Press ESC to close", "ui.labels.clickToCopy": "Click to copy", "ui.labels.copiedToClipboard": "Copied to clipboard", "ui.labels.noAudioAvailable": "No audio available", "ui.labels.noThumbnailAvailable": "No thumbnail available", "ui.labels.noPromptAvailable": "No prompt available", "ui.labels.videoModel": "Video Model", "ui.labels.speechModel": "Speech Model", "ui.labels.generatedSpeech": "Generated Speech", "ui.labels.generatedAudio": "Generated Audio", "ui.labels.defaultVoice": "Default Voice", "ui.labels.selectAnyVoice": "Select any voice", "ui.labels.cameraMotion": "Camera motion", "ui.labels.transform": "Transform", "ui.labels.transforming": "Transforming...", "ui.actions.showResult": "Show Result", "ui.actions.hideResult": "<PERSON><PERSON> Result", "ui.messages.imageLoaded": "Image loaded", "ui.messages.imageRevealComplete": "Image reveal complete", "ui.messages.processingImage": "Processing image", "ui.messages.videoLoaded": "Video loaded", "ui.messages.videoProcessing": "Video processing", "ui.messages.speechGenerating": "Generating speech...", "ui.messages.invalidDownloadLink": "Invalid download link", "ui.messages.pleaseSelectSupportedFile": "Please select a supported file", "ui.messages.dragDropOrClick": "Drag & drop files here or click to select", "ui.messages.dropFilesHere": "Drop files here", "ui.messages.selectMultipleFiles": "You can select multiple files", "ui.messages.selectSingleFile": "Select a file to upload", "ui.messages.supportedFormats": "Supported formats", "ui.messages.releaseToUpload": "Release to upload", "ui.messages.deleteConfirm": "Confirm Delete", "ui.messages.deleteFailed": "Delete failed", "ui.messages.youHaveNewNotification": "You have a new notification", "ui.messages.yourGenerationIsReady": "Your generation is ready", "ui.errors.errorLoadingImage": "Error loading image:", "ui.errors.failedToCopy": "Failed to copy: ", "ui.errors.failedToPlayAudioPreview": "Failed to play audio preview:", "ui.errors.wavesurferError": "<PERSON><PERSON><PERSON><PERSON> error:", "ui.errors.somethingWentWrong": "Something went wrong. Please try again. ", "ui.errors.supabaseUrlRequired": "Supabase URL and anonymous key are required", "dialog.startTypingHere": "Start typing dialog here...", "payment.debitCreditCard": "Debit or Credit Card", "payment.cardDescription": "Visa, Mastercard, American Express and more", "personGeneration": "Person Generation", "Imagen": "Imagen", "On": "On", "Off": "Off", "Prompts will always be refined to improve output quality": "Prompts will always be refined to improve output quality", "Prompts will not be modified": "Prompts will not be modified", "Tips": "Tips", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.", "Go to History": "Go to History", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Text To Speech OpenAI", "footer.privacyPolicy": "Privacy Policy", "footer.termsOfService": "Terms of Service", "footer.terms": "Terms", "footer.privacy": "Privacy", "Generate": "Generate", "Generate Video": "Generate Video", "ui.errors.generationFailed": "Generation failed", "ui.errors.unknownError": "An unknown error occurred", "ui.errors.tryAgainLater": "Please try again later", "downloadVideo": "Download Video", "imageStyles.selectImageStyle": "Select Image Style", "imageStyles.none.description": "No specific style applied", "imageStyles.3d-render.description": "Render image in 3D", "imageStyles.acrylic.description": "Create image with acrylic paint style", "imageStyles.anime-general.description": "Generate image in anime style", "imageStyles.creative.description": "Apply creative artistic effects", "imageStyles.dynamic.description": "Create dynamic and energetic visuals", "imageStyles.fashion.description": "Style image for fashion photography", "imageStyles.game-concept.description": "Design image for game concept art", "imageStyles.graphic-design-3d.description": "Apply 3D graphic design elements", "imageStyles.illustration.description": "Create illustration-style artwork", "imageStyles.portrait.description": "Optimize for portrait photography", "imageStyles.portrait-cinematic.description": "Create cinematic portrait style", "imageStyles.portrait-fashion.description": "Apply fashion portrait styling", "imageStyles.ray-traced.description": "Render with ray tracing effects", "imageStyles.stock-photo.description": "Create professional stock photo style", "imageStyles.watercolor.description": "Apply watercolor painting effects", "imageStyles.examples": "Examples", "downloadAudio": "Download Audio", "All Countries": "All Countries", "All Genders": "All Genders", "Country": "Country", "Gender": "Gender", "Reset": "Reset", "Search by name or description...": "Search by name or description...", "Male": "Male", "Female": "Female", "American": "American", "British": "British", "Australian": "Australian", "Indian": "Indian", "Chinese": "Chinese", "Spanish": "Spanish", "Canadian": "Canadian", "Irish": "Irish", "Singaporean": "Singaporean", "Russian": "Russian", "German": "German", "Portuguese": "Portuguese", "Hindi": "Hindi", "Mexican": "Mexican", "Latin American": "Latin American", "Argentine": "Argentine", "Peninsular": "Peninsular", "French": "French", "Parisian": "Parisian", "Standard": "Standard", "Brazilian": "Brazilian", "Turkish": "Turkish", "Istanbul": "Istanbul", "Bavarian": "Bavarian", "Polish": "Polish", "Italian": "Italian", "South African": "South African", "Scottish": "Scottish", "Welsh": "Welsh", "New Zealand": "New Zealand", "Dutch": "Dutch", "Belgian": "Belgian", "Swedish": "Swedish", "Norwegian": "Norwegian", "Danish": "Danish", "Korean": "Korean", "Korean, Seoul": "Korean, Seoul", "Japanese": "Japanese", "Croatian": "Croatian", "Czech": "Czech", "Moravian": "Moravian", "Zealandic": "Zealandic", "Indonesian": "Indonesian", "Javanese": "Javanese", "Romanian": "Romanian", "Swiss": "Swiss", "Vietnamese": "Vietnamese", "Arabic": "Arabic", "Bulgarian": "Bulgarian", "Finnish": "Finnish", "Greek": "Greek", "Hungarian": "Hungarian", "Filipino": "Filipino", "History": "History", "imagen-flash": "Gemini 2.0 Flash", "Detail": "Detail", "Delete": "Delete", "More": "More", "tts-text": "Audio", "tts-document": "Audio", "tts-multi-speaker": "Audio", "tts-history": "Audio", "tts-history_1": "Audio", "tts-history_2": "Audio", "tts-history_3": "Audio", "voice-training": "Voice Training", "voice-training_1": "Voice Training", "voice-training_2": "Voice Training", "Start writing or paste your text here or select a file to generate speech...": "Start writing or paste your text here or select a file to generate speech...", "Selecting a voice...": "Selecting a voice...", "Voices Library": "Voices Library", "Select a voice for your speaker from the library.": "Select a voice for your speaker from the library.", "Next": "Next", "Back": "Back", "Done": "Done", "I got it!": "I got it!", "Press ESC to close": "Press ESC to close", "Your generated image will appear here": "Your generated image will appear here", "Generate Speech": "Generate Speech", "Start writing or paste your text here to generate speech...": "Start writing or paste your text here to generate speech...", "Home": "Home", "Price per 1 character: {cost} Credits": "Price per 1 character: {cost} Credits", "veo-2": "Veo 2", "veo-3": "Veo 3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.", "Create Another": "Create Another", "estimated_credit": "Estimated Credit", "tts-flash": "Gemini 2.5 Flash", "Select Another Voice": "Select Another Voice", "Custom Prompt": "Custom Prompt", "Your credits will never expire.": "Your credits will never expire.", "Available credits": "Available credits", "{n}+ Styles": "{n}+ Styles", "Create images from text prompts.": "Create images from text prompts.", "/Image": "/Image", "/Video": "/Video", "/1 character": "/1 character", "Buy credits": "Buy credits", "My Account": "My Account", "Manage your account, credits, and orders.": "Manage your account, credits, and orders.", "Full Name": "Full Name", "Total Available Credits": "Total Available Credits", "Locked Credits": "Locked Credits", "Save changes": "Save changes", "Your account has been updated.": "Your account has been updated.", "User Info": "User Info", "Email": "Email", "Used to sign in, for email receipts and product updates.": "Used to sign in, for email receipts and product updates.", "Active and valid credits only": "Active and valid credits only", "We lock your credits to perform transactions.": "We lock your credits to perform transactions.", "Referral Link": "Referral Link", "Share your referral link to earn credits.": "Share your referral link to earn credits.", "Referral Code": "Referral Code", "Your Referral Code": "Your Referral Code", "Copy": "Copy", "Copied!": "Copied!", "Orders": "Orders", "Manage your orders.": "Manage your orders.", "Will appear on receipts, invoices, and other communication.": "Will appear on receipts, invoices, and other communication.", "User Information": "User Information", "Change Password": "Change Password", "Security": "Security", "Credit Statistics": "Credit Statistics", "enhance_prompt": "Enhance Prompt", "Current Plan": "Current Plan", "When you buy credits, you will be upgraded to Premium Plan.": "When you buy credits, you will be upgraded to Premium Plan.", "Available Credits": "Available Credits", "Purchased Credits": "Purchased Credits", "Plan Credits": "Plan Credits", "profile.passwordChanged": "Password Changed", "profile.passwordChangedDescription": "Your password has been changed successfully", "profile.passwordChangeError": "Password Change Failed", "profile.passwordChangeErrorDescription": "There was an error changing your password. Please try again.", "delete": "Delete", "profile.deleteAccount": "Delete Account", "profile.deleteAccountConfirmation": "Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.", "profile.accountDeleted": "Account Deleted", "profile.accountDeletedDescription": "Your account has been deleted successfully", "profile.accountDeletionError": "Account Deletion Failed", "profile.accountDeletionErrorDescription": "There was an error deleting your account. Please try again.", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.", "Check now": "Check now", "payment.success.title": "Payment Successful!", "payment.success.message": "Thank you for your purchase! Your payment has been processed successfully.", "payment.success.orderId": "Order ID:", "payment.success.redirecting": "Redirecting to your orders in {seconds} seconds...", "payment.success.viewOrders": "View My Orders", "payment.error.title": "Payment Error", "payment.error.message": "There was an issue processing your payment. Please contact support if this continues.", "payment.error.backToOrders": "Back to Orders", "Overview of your credits status.": "Overview of your credits status.", "Payment History": "Payment History", "Your payment history will appear here once you have made a purchase.": "Your payment history will appear here once you have made a purchase.", "Payment method": "Payment method", "Purchase Date": "Purchase Date", "Amount": "Amount", "Status": "Status", "Payment amount": "Payment amount", "payment.status.unavailable": "Unavailable", "payment.status.created": "Created", "payment.status.completed": "Completed", "payment.status.failed": "Failed", "payment.status.canceled": "Canceled", "payment.status.processing": "Processing", "payment.status.refund": "Refund", "payment.status.partial_paid": "Partial Paid", "Integration": "Integration", "API Keys": "API Keys", "Manage your API keys.": "Manage your API keys.", "Create API Key": "Create API Key", "Name your API key.": "Name your API key.", "Create": "Create", "You have not created any API keys yet.": "You have not created any API keys yet.", "Copy API Key": "Copy API Key", "Delete API Key": "Delete API Key", "EMAIL_NOT_EXIST": "Email does not exist", "Your account is not verified": "Your account is not verified", "Your account is not verified. Please verify your account to continue": "Your account is not verified. Please verify your account to continue", "TOKEN_USED": "Token already used", "NOT_ENOUGH_CREDIT": "Not enough credit. Please top up your account.", "Not enough credit": "Not enough credit", "Your account does not have enough credit. Please top up your account to continue.": "Your account does not have enough credit. Please top up your account to continue.", "{n} credits": "{n} credits", "USD / {unit}": "USD / {unit}", "Credits / {unit}": "Credits / {unit}", "Save {n}%": "Save {n}%", "${price} = {n} credits": "${price} = {n} credits", "You can switch between money and credits to see the price in your preferred currency.": "You can switch between money and credits to see the price in your preferred currency.", "Forever": "Forever", "For large organizations.": "For large organizations.", "Free": "Free", "Contact us": "Contact us", "Contact sales": "Contact sales", "Premium": "Premium", "Enterprise": "Enterprise", "Show money": "Show money", "Show credits": "Show credits", "Auto upgrade after buy credits": "Auto upgrade after buy credits", "Image": "Image", "Video": "Video", "Audio": "Audio", "Dialog": "Dialog", "Get started": "Get started", "Contact": "Contact", "Image Style": "Image Style", "Image Aspect Ratio": "Image Aspect Ratio", "Enhance Prompt": "Enhance Prompt", "Aspect Ratio": "Aspect Ratio", "Support multiple aspect ratio": "Support multiple aspect ratio", "Support enhance prompt": "Support enhance prompt", "Up to {size}MB": "Up to {size}MB", "SIGNUP_MAIL_EXIST": "Email already exists", "SIGNIN_USER_NOT_FOUND": "User not found", "SIGNIN_USER_NOT_VERIFIED": "User not verified", "SIGNIN_USER_DISABLED": "User disabled", "SIGNIN_WRONG_PASSWORD": "Wrong password", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "User not found for email", "SIGNIN_INVALID_EMAIL": "Invalid email", "auth.accountCreated": "Account created", "auth.accountCreatedDescription": "Your account has been created successfully. Please check your email to verify your account."}